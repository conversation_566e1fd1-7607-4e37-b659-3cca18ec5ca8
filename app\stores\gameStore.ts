import type {
  GameState,
  GameMode,
  GameItem,
  Problem,
  NotationType,
  FunctionType,
  GameStats
} from '~/types/game'
import { generateNotationProblem, generateDomainRangeProblem } from '~/utils/problemGenerator'

export const useGameStore = defineStore('game', () => {
  // State
  const state = ref<GameState>({
    currentMode: null,
    items: {},
    totalCompleted: 0,
    startTime: undefined,
    endTime: undefined
  })

  // Getters
  const isGameActive = computed(() => state.value.currentMode !== null)
  const completedItems = computed(() =>
    Object.values(state.value.items).filter(item => item.completed)
  )
  const completionPercentage = computed(() =>
    Math.round((state.value.totalCompleted / 37) * 100)
  )
  const isGameComplete = computed(() => state.value.totalCompleted === 37)

  // Game statistics
  const gameStats = computed((): GameStats => {
    const items = Object.values(state.value.items)
    const totalAttempts = items.reduce((sum, item) => sum + item.attempts, 0)
    const correctAnswers = items.filter(item => item.completed).length

    let totalTime = 0
    let averageTime = 0
    if (state.value.startTime && state.value.endTime) {
      totalTime = Math.round((state.value.endTime.getTime() - state.value.startTime.getTime()) / 1000) // in seconds
      averageTime = correctAnswers > 0 ? totalTime / correctAnswers : 0
    }

    return {
      totalAttempts,
      correctAnswers,
      averageTime,
      totalTime,
      completionRate: totalAttempts > 0 ? correctAnswers / totalAttempts : 0
    }
  })

  // Actions
  const initializeGame = (mode: GameMode) => {
    state.value.currentMode = mode
    state.value.startTime = new Date()
    state.value.endTime = undefined
    state.value.totalCompleted = 0

    // Initialize all 37 game items
    const items: Record<number, GameItem> = {}
    for (let i = 1; i <= 37; i++) {
      items[i] = {
        id: i,
        completed: false,
        attempts: 0,
        lastAttemptTime: undefined
      }
    }
    state.value.items = items
  }

  const selectItem = (itemId: number) => {
    if (state.value.items[itemId]?.completed) {
      return false // Don't allow selecting completed items
    }

    // Increment attempts for this item
    if (state.value.items[itemId]) {
      state.value.items[itemId].attempts++
      state.value.items[itemId].lastAttemptTime = new Date()
    }

    return true // Item can be selected
  }



  const submitAnswer = (isCorrect: boolean, currentItemId?: number) => {
    if (!currentItemId) return

    const item = state.value.items[currentItemId]
    if (!item) return

    if (isCorrect) {
      item.completed = true
      state.value.totalCompleted++

      // Check if game is complete
      if (state.value.totalCompleted === 37) {
        state.value.endTime = new Date()
        // Navigate to completion page based on current game mode
        navigateToCompletion()
      }
    }
  }

  const navigateToCompletion = () => {
    if (process.client && state.value.currentMode) {
      const router = useRouter()
      const completionPath = `/${state.value.currentMode}/complete`
      router.push(completionPath)
    }
  }

  const resetGame = () => {
    state.value.currentMode = null
    state.value.items = {}
    state.value.totalCompleted = 0
    state.value.startTime = undefined
    state.value.endTime = undefined
  }

  return {
    // State
    state: readonly(state),

    // Getters
    isGameActive,
    completedItems,
    completionPercentage,
    isGameComplete,
    gameStats,

    // Actions
    initializeGame,
    selectItem,
    submitAnswer,
    resetGame,
    navigateToCompletion
  }
})