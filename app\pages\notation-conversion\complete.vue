<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
    <UContainer class="py-12">
      <!-- Completion Header -->
      <div class="text-center mb-12">
        <div class="flex justify-center mb-6">
          <div class="w-24 h-24 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center">
            <Icon name="i-lucide-trophy" class="w-12 h-12 text-yellow-500" />
          </div>
        </div>
        
        <h1 class="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-4">
          Congratulations!
        </h1>
        
        <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          You've successfully completed all 37 notation conversion problems!
        </p>
      </div>

      <!-- Game Statistics -->
      <UCard class="max-w-2xl mx-auto mb-8">
        <template #header>
          <div class="flex items-center space-x-3">
            <Icon name="i-lucide-graduation-cap" class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            <h2 class="text-xl font-semibold">Your Performance</h2>
          </div>
        </template>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">37</div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Problems Completed</div>
          </div>
          
          <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <div class="text-3xl font-bold text-green-600 dark:text-green-400">
              {{ gameStore.gameStats.totalAttempts }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Total Attempts</div>
          </div>
          
          <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
            <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">
              {{ Math.round(gameStore.gameStats.completionRate * 100) }}%
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-400">Success Rate</div>
          </div>
        </div>

        <div v-if="gameStore.gameStats.totalTime" class="mt-6 text-center p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div class="text-2xl font-bold text-gray-700 dark:text-gray-300">
            {{ formatTime(gameStore.gameStats.totalTime) }}
          </div>
          <div class="text-sm text-gray-600 dark:text-gray-400">Total Time</div>
        </div>
      </UCard>

      <!-- Achievement Badge -->
      <div class="text-center mb-8">
        <div class="inline-flex items-center space-x-2 px-6 py-3 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 rounded-full border border-yellow-200 dark:border-yellow-700">
          <Icon name="i-lucide-award" class="w-5 h-5" />
          <span class="font-semibold">Notation Conversion Expert</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
        <UButton 
          @click="restartGame" 
          size="lg" 
          class="flex-1"
          :loading="restarting"
        >
          <Icon name="i-lucide-refresh-cw" class="w-5 h-5 mr-2" />
          Play Again
        </UButton>
        
        <UButton 
          @click="$router.push('/')" 
          variant="outline" 
          size="lg" 
          class="flex-1"
        >
          <Icon name="i-lucide-home" class="w-5 h-5 mr-2" />
          Back to Home
        </UButton>
      </div>

      <!-- Try Other Game -->
      <div class="text-center mt-8">
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Ready for another challenge?
        </p>
        <UButton 
          @click="$router.push('/domain-range')" 
          variant="soft" 
          color="green"
        >
          <Icon name="i-lucide-bar-chart-3" class="w-5 h-5 mr-2" />
          Try Domain & Range
        </UButton>
      </div>
    </UContainer>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useSeoMeta({
  title: 'Game Complete - Notation Conversion Game',
  description: 'Congratulations on completing all notation conversion problems!',
})

// Store
const gameStore = useGameStore()
const router = useRouter()

// State
const restarting = ref(false)

// Redirect if game is not complete
onMounted(() => {
  if (!gameStore.isGameComplete) {
    router.push('/notation-conversion')
  }
})

// Handlers
const restartGame = async () => {
  restarting.value = true
  try {
    gameStore.resetGame()
    gameStore.initializeGame('notation-conversion')
    await router.push('/notation-conversion')
  } finally {
    restarting.value = false
  }
}

// Utility function to format time
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`
  }
  return `${remainingSeconds}s`
}
</script>
