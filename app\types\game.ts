// Game type definitions for the educational math application

export type GameMode = 'notation-conversion' | 'domain-range'

export type NotationType = 'algebraic' | 'interval' | 'set-builder'

export type FunctionType = 'linear' | 'quadratic' | 'cubic' | 'absolute' | 'square-root'

export interface GameItem {
  id: number
  completed: boolean
  attempts: number
  lastAttemptTime?: Date
}

export interface NotationProblem {
  id: string
  type: 'notation-conversion'
  sourceNotation: NotationType
  targetNotation: NotationType
  problem: string
  correctAnswers: string[]
  hint?: string
}

export interface DomainRangeProblem {
  id: string
  type: 'domain-range'
  functionType: FunctionType
  equation: string
  graph?: string // SVG or image path
  questionType: 'domain' | 'range' | 'both'
  correctDomain?: string[]
  correctRange?: string[]
  hint?: string
}

export type Problem = NotationProblem | DomainRangeProblem

export interface GameState {
  currentMode: GameMode | null
  items: Record<number, GameItem>
  totalCompleted: number
  startTime?: Date
  endTime?: Date
}

export interface GameStats {
  totalAttempts: number
  correctAnswers: number
  averageTime: number
  totalTime: number
  completionRate: number
}

export interface MathKeyboardKey {
  symbol: string
  label: string
  category: 'number' | 'operator' | 'interval' | 'set' | 'inequality' | 'special'
  latex?: string
}

export interface ValidationResult {
  isCorrect: boolean
  message: string
  acceptedAnswer?: string
}

export interface ProblemGeneratorOptions {
  difficulty?: 'easy' | 'medium' | 'hard'
  excludeTypes?: string[]
  includeHints?: boolean
}