<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <UContainer class="py-8">
      <!-- Header -->
      <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
          <UButton
            @click="$router.push('/')"
            variant="ghost"
            icon="i-lucide-arrow-left"
            size="lg"
          >
            Back to Home
          </UButton>
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              Domain & Range Game
            </h1>
            <p class="text-gray-600 dark:text-gray-300">
              Find the domain and range of mathematical functions
            </p>
          </div>
        </div>

        <!-- Progress -->
        <div class="text-right">
          <div class="text-2xl font-bold text-green-600 dark:text-green-400">
            {{ gameStore.completionPercentage }}%
          </div>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ gameStore.state.totalCompleted }} / 37 completed
          </div>
        </div>
      </div>

      <!-- Progress Bar -->
      <div class="mb-8">
        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
          <span>Progress</span>
          <span>{{ gameStore.state.totalCompleted }} / 37</span>
        </div>
        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
          <div
            class="bg-green-600 h-3 rounded-full transition-all duration-500"
            :style="{ width: `${gameStore.completionPercentage}%` }"
          ></div>
        </div>
      </div>

      <!-- Game Grid -->
      <GameGrid />
    </UContainer>
  </div>
</template>

<script setup lang="ts">
// SEO Meta
useSeoMeta({
  title: 'Domain & Range Game - Math Game Center',
  description: 'Practice finding the domain and range of various mathematical functions.',
})

// Store
const gameStore = useGameStore()

// Initialize game on mount
onMounted(() => {
  gameStore.initializeGame('domain-range')
})
</script>